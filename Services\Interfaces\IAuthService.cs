﻿using LSB.SellerMailTracker.API.DTOs;
using LSB.SellerMailTracker.API.Models;

namespace LSB.SellerMailTracker.API.Services.Interfaces
{
    public interface IAuthService
    {
        Task<AuthUrlResponse> CreateAuthUrlAsync(CreateAuthUrlRequest request);
        Task<OAuth2CallbackResult> ProcessOAuth2CallbackAsync(string code, string state, string? error = null);
        Task<AuthUrlResponse> GetAuthLinkAsync();
        Task<bool> RefreshTokenAsync(string accountId);
    }
}