[{"ContainingType": "LSB.SellerMailTracker.API.Controllers.AccountsController", "Method": "GetAccounts", "RelativePath": "api/Accounts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.AccountsController", "Method": "DeleteAccount", "RelativePath": "api/Accounts/{accountId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "accountId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.AuthController", "Method": "CreateAuthUrl", "RelativePath": "api/Auth/auth-url", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LSB.SellerMailTracker.API.DTOs.CreateAuthUrlRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.AuthController", "Method": "GetAuthLink", "RelativePath": "api/Auth/get-auth-link", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh-token/{accountId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "accountId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.AutoScanSettingsController", "Method": "GetAutoScanLogs", "RelativePath": "api/AutoScanSettings/logs", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[LSB.SellerMailTracker.API.Controllers.AutoScanLogResponse, LSB.SellerMailTracker.API, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.AutoScanSettingsController", "Method": "RunAutoScanNow", "RelativePath": "api/AutoScanSettings/run-now", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "LSB.SellerMailTracker.API.Controllers.AutoScanRunResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.AutoScanSettingsController", "Method": "GetAutoScanSettings", "RelativePath": "api/AutoScanSettings/settings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "LSB.SellerMailTracker.API.Controllers.AutoScanSettingsResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.AutoScanSettingsController", "Method": "SaveAutoScanSettings", "RelativePath": "api/AutoScanSettings/settings", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LSB.SellerMailTracker.API.Controllers.AutoScanSettingsRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "LSB.SellerMailTracker.API.Controllers.AutoScanSettingsResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.AutoScanSettingsController", "Method": "GetAutoScanStatus", "RelativePath": "api/AutoScanSettings/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "LSB.SellerMailTracker.API.Controllers.AutoScanStatusResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.AutoScanSettingsController", "Method": "ToggleAutoScan", "RelativePath": "api/AutoScanSettings/toggle", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "LSB.SellerMailTracker.API.Controllers.AutoScanSettingsResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.DashboardController", "Method": "GetDashboardStats", "RelativePath": "api/Dashboard/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.EmailProcessingController", "Method": "GetDashboardStats", "RelativePath": "api/EmailProcessing/dashboard-stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "LSB.SellerMailTracker.API.Controllers.DashboardResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.EmailProcessingController", "Method": "GetAllTemplates", "RelativePath": "api/EmailProcessing/debug/templates", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.EmailProcessingController", "Method": "TestTemplate", "RelativePath": "api/EmailProcessing/debug/test-template", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LSB.SellerMailTracker.API.Controllers.TestTemplateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.EmailProcessingController", "Method": "ProcessMultipleAccounts", "RelativePath": "api/EmailProcessing/process-multiple-accounts", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LSB.SellerMailTracker.API.Controllers.BatchProcessRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "LSB.SellerMailTracker.API.Controllers.BatchProcessResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.EmailProcessingController", "Method": "ProcessSingleAccount", "RelativePath": "api/EmailProcessing/process-single-account", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LSB.SellerMailTracker.API.Controllers.ProcessAccountRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "LSB.SellerMailTracker.API.Controllers.ProcessAccountResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.EmailProcessingController", "Method": "GetProcessedEmails", "RelativePath": "api/EmailProcessing/processed-emails", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "accountId", "Type": "System.String", "IsRequired": false}, {"Name": "filterId", "Type": "System.String", "IsRequired": false}, {"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "LSB.SellerMailTracker.API.Controllers.PagedEmailResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.FiltersController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Filters", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "LSB.SellerMailTracker.API.Controllers.CreateFilterRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.FiltersController", "Method": "GetFilters", "RelativePath": "api/Filters", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.FiltersController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Filters/{filterId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "filterId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.FiltersController", "Method": "UpdateFilter", "RelativePath": "api/Filters/{filterId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "filterId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "LSB.SellerMailTracker.API.Controllers.UpdateFilterRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.FiltersController", "Method": "DeleteFilter", "RelativePath": "api/Filters/{filterId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "filterId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.FiltersController", "Method": "Create<PERSON>ncomeFilter", "RelativePath": "api/Filters/quick/income", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.FiltersController", "Method": "CreateOutcomeFilter", "RelativePath": "api/Filters/quick/outcome", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.FiltersController", "Method": "GetFilterStats", "RelativePath": "api/Filters/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.FiltersController", "Method": "GetTemplates", "RelativePath": "api/Filters/templates", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.FiltersController", "Method": "GetTemplate", "RelativePath": "api/Filters/templates/{templateId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "LSB.SellerMailTracker.API.Controllers.AuthController", "Method": "OAuth2Callback", "RelativePath": "OAuth2Callback", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "code", "Type": "System.String", "IsRequired": false}, {"Name": "state", "Type": "System.String", "IsRequired": false}, {"Name": "error", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}]