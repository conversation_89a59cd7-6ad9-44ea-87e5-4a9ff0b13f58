﻿using LSB.SellerMailTracker.API.DTOs;
using LSB.SellerMailTracker.API.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace LSB.SellerMailTracker.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IAuthService authService, ILogger<AuthController> logger)
        {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// Create auth URL and immediately save seller as pending account
        /// </summary>
        [HttpPost("auth-url")]
        public async Task<IActionResult> CreateAuthUrl([FromBody] CreateAuthUrlRequest request)
        {
            var result = await _authService.CreateAuthUrlAsync(request);

            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }

        /// <summary>
        /// OAuth callback - updates existing pending account
        /// </summary>
        [HttpGet("/OAuth2Callback")]
        public async Task<IActionResult> OAuth2Callback(string code, string state, string error = null)
        {
            var result = await _authService.ProcessOAuth2CallbackAsync(code, state, error);

            return Content(
                GenerateResultHtml(
                    result.Success,
                    result.Message,
                    result.Details,
                    result.AccountEmail,
                    result.SellerName
                ),
                "text/html"
            );
        }

        /// <summary>
        /// Get authentication link for user to click (kept for backward compatibility) 
        /// </summary>
        [HttpGet("get-auth-link")]
        public async Task<IActionResult> GetAuthLink()
        {
            var result = await _authService.GetAuthLinkAsync();

            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }

        /// <summary>
        /// Refresh OAuth token for an account
        /// </summary>
        [HttpPost("refresh-token/{accountId}")]
        public async Task<IActionResult> RefreshToken(string accountId)
        {
            try
            {
                var success = await _authService.RefreshTokenAsync(accountId);
                if (success)
                {
                    return Ok(new { message = "Token refreshed successfully" });
                }
                else
                {
                    return BadRequest(new { error = "Failed to refresh token" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing token for account {AccountId}", accountId);
                return BadRequest(new { error = ex.Message });
            }
        }

        #region Private Helper Methods

        private string GenerateResultHtml(bool success, string message, string details, string? accountEmail = null, string? sellerName = null)
        {
            var statusIcon = success ? "✓" : "✕";
            var titleText = success ? "Kết nối thành công!" : "Kết nối thất bại";
            var statusClass = success ? "success" : "error";
            var primaryColor = success ? "#10b981" : "#ef4444";
            var gradientBg = success ? "linear-gradient(135deg, #10b981, #059669)" : "linear-gradient(135deg, #ef4444, #dc2626)";

            // Create personalized message with seller name
            var personalizedMessage = success && !string.IsNullOrEmpty(sellerName)
                ? $"Chào mừng {sellerName}! {message}"
                : message;

            var accountInfoHtml = "";
            if (success && !string.IsNullOrEmpty(accountEmail))
            {
                var sellerRow = !string.IsNullOrEmpty(sellerName)
                    ? $@"
                <div class='account-detail'>
                    <span class='label'>Seller:</span>
                    <span class='value'>{sellerName}</span>
                </div>"
                    : "";

                accountInfoHtml = $@"
            <div class='account-info'>
                <h3>
                    <span class='icon'>📧</span>
                    Thông tin tài khoản
                </h3>
                <div class='account-details'>
                    <div class='account-detail'>
                        <span class='label'>Email:</span>
                        <span class='value'>{accountEmail}</span>
                    </div>
                    {sellerRow}
                    <div class='account-detail'>
                        <span class='label'>Trạng thái:</span>
                        <span class='value'>Đã kích hoạt</span>
                    </div>
                </div>
            </div>";
            }

            return $@"
<!DOCTYPE html>
<html lang='vi'>
<head>
    <meta charset='utf-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <title>Kết quả xác thực - LSB Seller Mail Tracker</title>
    <link href='https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap' rel='stylesheet'>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }}

        body::before {{
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: float 20s ease-in-out infinite;
            pointer-events: none;
        }}

        @keyframes float {{
            0%, 100% {{ transform: translateY(0px) rotate(0deg); }}
            50% {{ transform: translateY(-20px) rotate(180deg); }}
        }}

        @keyframes fadeIn {{
            from {{ opacity: 0; transform: translateY(30px); }}
            to {{ opacity: 1; transform: translateY(0); }}
        }}

        @keyframes scaleIn {{
            from {{ transform: scale(0.8); opacity: 0; }}
            to {{ transform: scale(1); opacity: 1; }}
        }}

        @keyframes pulse {{
            0%, 100% {{ transform: scale(1); }}
            50% {{ transform: scale(1.05); }}
        }}

        @keyframes countdown {{
            from {{ width: 100%; }}
            to {{ width: 0%; }}
        }}

        .container {{
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            box-shadow: 
                0 32px 64px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.1) inset;
            padding: 48px;
            max-width: 560px;
            width: 100%;
            text-align: center;
            position: relative;
            animation: scaleIn 0.6s ease-out;
        }}

        .status-icon {{
            width: 120px;
            height: 120px;
            margin: 0 auto 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            font-weight: 700;
            position: relative;
            animation: fadeIn 0.8s ease-out 0.2s both;
        }}

        .status-icon.success {{
            background: {gradientBg};
            color: white;
            box-shadow: 0 20px 40px rgba(16, 185, 129, 0.3);
            animation: fadeIn 0.8s ease-out 0.2s both, pulse 2s ease-in-out infinite;
        }}

        .status-icon.error {{
            background: {gradientBg};
            color: white;
            box-shadow: 0 20px 40px rgba(239, 68, 68, 0.3);
        }}

        .status-icon::before {{
            content: '';
            position: absolute;
            inset: -4px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(255,255,255,0.3), transparent);
            z-index: -1;
        }}

        h1 {{
            color: #1f2937;
            margin-bottom: 16px;
            font-size: 32px;
            font-weight: 700;
            letter-spacing: -0.02em;
            animation: fadeIn 1s ease-out 0.4s both;
        }}

        .subtitle {{
            color: #6b7280;
            font-size: 18px;
            font-weight: 400;
            margin-bottom: 32px;
            line-height: 1.5;
            animation: fadeIn 1s ease-out 0.6s both;
        }}

        .account-info {{
            background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
            border: 1px solid #10b981;
            border-radius: 16px;
            padding: 24px;
            margin: 32px 0;
            position: relative;
            overflow: hidden;
            animation: fadeIn 1s ease-out 0.8s both;
        }}

        .account-info::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #10b981, #059669, #047857);
        }}

        .account-info h3 {{
            color: #065f46;
            margin-bottom: 16px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }}

        .account-details {{
            display: grid;
            gap: 12px;
        }}

        .account-detail {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: rgba(16, 185, 129, 0.08);
            border-radius: 10px;
            font-size: 14px;
            border: 1px solid rgba(16, 185, 129, 0.1);
        }}

        .account-detail .label {{
            color: #047857;
            font-weight: 500;
        }}

        .account-detail .value {{
            color: #065f46;
            font-weight: 600;
        }}

        .details-section {{
            background: #f9fafb;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            color: #4b5563;
            font-size: 15px;
            line-height: 1.6;
            border: 1px solid #e5e7eb;
        }}

        .action-buttons {{
            display: flex;
            gap: 16px;
            margin-top: 32px;
            animation: fadeIn 1s ease-out 1.2s both;
        }}

        .btn {{
            flex: 1;
            padding: 16px 24px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }}

        .btn::before {{
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }}

        .btn:hover::before {{
            left: 100%;
        }}

        .btn-primary {{
            background: {gradientBg};
            color: white;
            box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
        }}

        .btn-primary:hover {{
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
        }}

        .btn-secondary {{
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            color: #475569;
            border: 1px solid #cbd5e1;
        }}

        .btn-secondary:hover {{
            background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
            transform: translateY(-1px);
        }}

        .countdown-container {{
            margin-top: 24px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.03);
            border-radius: 16px;
            animation: fadeIn 1s ease-out 1.4s both;
        }}

        .countdown-text {{
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 12px;
        }}

        .countdown-timer {{
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #1f2937, #374151);
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            font-weight: 600;
            font-family: 'Monaco', 'Menlo', monospace;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }}

        .progress-bar {{
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 16px;
        }}

        .progress-fill {{
            height: 100%;
            background: {gradientBg};
            border-radius: 3px;
            animation: countdown 10s linear;
        }}

        .icon {{
            font-size: 20px;
        }}

        /* Responsive Design */
        @media (max-width: 640px) {{
            .container {{
                padding: 32px 24px;
                margin: 16px;
            }}

            h1 {{
                font-size: 24px;
            }}

            .subtitle {{
                font-size: 16px;
            }}

            .status-icon {{
                width: 100px;
                height: 100px;
                font-size: 40px;
            }}

            .action-buttons {{
                flex-direction: column;
            }}
        }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='status-icon {statusClass}'>
            {statusIcon}
        </div>
        
        <h1>{titleText}</h1>
        <p class='subtitle'>{personalizedMessage}</p>
        
        {accountInfoHtml}
        
        <div class='details-section'>
            {details}
        </div>
        
        <div class='action-buttons'>
            <button class='btn btn-primary' onclick='closeWindow()'>
                {(success ? "Hoàn tất" : "Thử lại")}
            </button>
            <button class='btn btn-secondary' onclick='window.location.reload()'>
                Làm mới
            </button>
        </div>
        
        <div class='countdown-container'>
            <div class='countdown-text'>Tự động đóng sau:</div>
            <div class='countdown-timer'>
                <span>⏱️</span>
                <span id='countdown'>10</span>
                <span>giây</span>
            </div>
            <div class='progress-bar'>
                <div class='progress-fill'></div>
            </div>
        </div>
    </div>

    <script>
        let seconds = 10;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {{
            seconds--;
            countdownElement.textContent = seconds;
            
            if (seconds <= 3) {{
                countdownElement.parentElement.style.animation = 'pulse 0.5s ease-in-out infinite';
                countdownElement.parentElement.style.background = 'linear-gradient(135deg, #ef4444, #dc2626)';
            }}
            
            if (seconds <= 0) {{
                clearInterval(timer);
                closeWindow();
            }}
        }}, 1000);
        
        function closeWindow() {{
            document.querySelector('.container').style.animation = 'scaleIn 0.3s ease-in reverse';
            setTimeout(() => {{
                window.close();
            }}, 300);
        }}
        
        document.addEventListener('keydown', function(e) {{
            if (e.key === 'Escape' || e.key === 'Enter') {{
                clearInterval(timer);
                closeWindow();
            }}
        }});

        document.querySelectorAll('.btn').forEach(btn => {{
            btn.addEventListener('mouseenter', function() {{
                this.style.transform = 'translateY(-2px) scale(1.02)';
            }});
            
            btn.addEventListener('mouseleave', function() {{
                this.style.transform = 'translateY(0) scale(1)';
            }});
        }});
    </script>
</body>
</html>";
        }

        #endregion
    }
}