﻿using System.Text.Json;
using System.Text;
using System.Net;
using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;
using LSB.SellerMailTracker.API.DTOs;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class GmailService : IGmailService
    {
        private readonly IDataService _dataService;
        private readonly HttpClient _httpClient;
        private readonly string _clientId;
        private readonly string _clientSecret;
        private readonly string _redirectUri;
        private bool _disposed = false;

        public GmailService(IDataService dataService, IConfiguration configuration)
        {
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));

            // ✅ Tạo HttpClient với proper configuration
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromMinutes(10);
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "LSB.SellerMailTracker/1.0");

            _clientId = configuration["GoogleOAuth:ClientId"] ??
                        throw new InvalidOperationException("Gmail ClientId not configured");
            _clientSecret = configuration["GoogleOAuth:ClientSecret"] ??
                           throw new InvalidOperationException("Gmail ClientSecret not configured");
            _redirectUri = configuration["GoogleOAuth:RedirectUri"] ??
                          throw new InvalidOperationException("Gmail RedirectUri not configured");
        }

        public async Task<string> GetAuthorizationUrlAsync()
        {
            var scopes = Uri.EscapeDataString("https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/gmail.modify");
            var state = Guid.NewGuid().ToString();

            var authUrl = $"https://accounts.google.com/o/oauth2/v2/auth?" +
                         $"client_id={_clientId}&" +
                         $"redirect_uri={Uri.EscapeDataString(_redirectUri)}&" +
                         $"scope={scopes}&" +
                         $"response_type=code&" +
                         $"access_type=offline&" +
                         $"prompt=consent&" +
                         $"state={state}";

            return authUrl;
        }

        /// <summary>
        /// Get OAuth 2.0 authorization URL with state data embedded
        /// </summary>
        /// <param name="stateData">State data to embed in authorization URL</param>
        /// <returns>Authorization URL string</returns>
        public async Task<string> GetAuthorizationUrlAsync(object stateData)
        {
            var scopes = Uri.EscapeDataString("https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/gmail.modify");

            // Serialize state data to JSON and encode it
            var stateJson = JsonSerializer.Serialize(stateData);
            Console.WriteLine($"🔍 State JSON being sent: {stateJson}");

            var stateEncoded = Convert.ToBase64String(Encoding.UTF8.GetBytes(stateJson));
            Console.WriteLine($"🔍 State encoded: {stateEncoded}");

            var authUrl = $"https://accounts.google.com/o/oauth2/v2/auth?" +
                         $"client_id={_clientId}&" +
                         $"redirect_uri={Uri.EscapeDataString(_redirectUri)}&" +
                         $"scope={scopes}&" +
                         $"response_type=code&" +
                         $"access_type=offline&" +
                         $"prompt=consent&" +
                         $"state={Uri.EscapeDataString(stateEncoded)}";

            Console.WriteLine($"🔗 Generated auth URL: {authUrl.Substring(0, Math.Min(100, authUrl.Length))}...");

            return await Task.FromResult(authUrl);
        }

        /// <summary>
        /// Process OAuth callback and return account information with seller data
        /// </summary>
        /// <param name="authorizationCode">Authorization code from OAuth callback</param>
        /// <param name="state">State parameter from OAuth callback</param>
        /// <returns>Gmail account with embedded seller information</returns>
        public async Task<GmailAccount> ProcessAuthCallbackAsync(string authorizationCode, string state)
        {
            Console.WriteLine($"🚀 ProcessAuthCallbackAsync called!");
            Console.WriteLine($"🔍 Authorization Code: {authorizationCode?.Substring(0, Math.Min(20, authorizationCode?.Length ?? 0))}...");
            Console.WriteLine($"🔍 State received: {state}");

            try
            {
                // Exchange authorization code for tokens
                var tokenResponse = await ExchangeCodeForTokensAsync(authorizationCode);
                var userEmail = await GetAccountEmailAsync(tokenResponse.AccessToken);

                Console.WriteLine($"📧 User email: {userEmail}");

                // Create base account
                var account = new GmailAccount
                {
                    Id = Guid.NewGuid().ToString(),
                    Email = userEmail,
                    DisplayName = userEmail, // Will be updated below if seller info exists
                    AccessToken = tokenResponse.AccessToken,
                    RefreshToken = tokenResponse.RefreshToken,
                    TokenExpiry = DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn),
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    LastSyncAt = DateTime.UtcNow
                };

                Console.WriteLine($"🔍 Initial DisplayName: {account.DisplayName}");

                // Try to decode and extract seller info from state parameter
                if (!string.IsNullOrEmpty(state))
                {
                    Console.WriteLine($"🔍 Processing state parameter...");
                    try
                    {
                        var decodedState = Encoding.UTF8.GetString(Convert.FromBase64String(state));
                        Console.WriteLine($"🔍 Decoded state: {decodedState}");

                        using var stateDocument = JsonDocument.Parse(decodedState);
                        var root = stateDocument.RootElement;

                        // Extract seller info if present
                        if (root.TryGetProperty("sellerInfo", out var sellerInfoElement))
                        {
                            Console.WriteLine($"✅ SellerInfo found in state!");
                            Console.WriteLine($"🔍 SellerInfo JSON: {sellerInfoElement}");

                            var sellerInfo = new SellerInfo();

                            if (sellerInfoElement.TryGetProperty("name", out var nameElement))
                            {
                                sellerInfo.Name = nameElement.GetString() ?? "";
                                Console.WriteLine($"🔍 Extracted seller name: '{sellerInfo.Name}'");

                                // ✅ Use seller name as display name
                                if (!string.IsNullOrEmpty(sellerInfo.Name))
                                {
                                    account.DisplayName = sellerInfo.Name;
                                    Console.WriteLine($"✅ Updated DisplayName to: '{account.DisplayName}'");
                                }
                            }
                            else
                            {
                                Console.WriteLine($"❌ No 'name' property in sellerInfo");
                            }

                            if (sellerInfoElement.TryGetProperty("location", out var locationElement))
                            {
                                sellerInfo.Location = locationElement.GetString();
                                Console.WriteLine($"🔍 Extracted location: '{sellerInfo.Location}'");
                            }

                            if (sellerInfoElement.TryGetProperty("note", out var noteElement))
                            {
                                sellerInfo.Note = noteElement.GetString();
                                Console.WriteLine($"🔍 Extracted note: '{sellerInfo.Note}'");
                            }

                            if (sellerInfoElement.TryGetProperty("createdAt", out var createdAtElement))
                            {
                                if (DateTime.TryParse(createdAtElement.GetString(), out var createdAt))
                                {
                                    sellerInfo.CreatedAt = createdAt;
                                }
                            }
                            else
                            {
                                sellerInfo.CreatedAt = DateTime.UtcNow;
                            }

                            account.SellerInfo = sellerInfo;
                            Console.WriteLine($"✅ SellerInfo assigned to account!");
                        }
                        else
                        {
                            Console.WriteLine($"❌ NO 'sellerInfo' property found in state");
                        }

                        // Extract redirect URL if present
                        if (root.TryGetProperty("redirectUrl", out var redirectElement))
                        {
                            var redirectUrl = redirectElement.GetString();
                            Console.WriteLine($"📍 Redirect URL from state: {redirectUrl}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"❌ State parsing error: {ex.Message}");
                        Console.WriteLine($"❌ State parsing stack trace: {ex.StackTrace}");
                        // Continue without seller info - not critical for account creation
                    }
                }
                else
                {
                    Console.WriteLine($"❌ NO STATE PARAMETER RECEIVED!");
                }

                Console.WriteLine($"🔍 Final account details:");
                Console.WriteLine($"  - Email: {account.Email}");
                Console.WriteLine($"  - DisplayName: {account.DisplayName}");
                Console.WriteLine($"  - SellerInfo: {(account.SellerInfo != null ? account.SellerInfo.Name : "NULL")}");

                return account;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error in ProcessAuthCallbackAsync: {ex.Message}");
                Console.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<GmailAccount> AddAccountAsync(string authorizationCode)
        {
            var tokenResponse = await ExchangeCodeForTokensAsync(authorizationCode);
            var userEmail = await GetAccountEmailAsync(tokenResponse.AccessToken);

            var account = new GmailAccount
            {
                Id = Guid.NewGuid().ToString(),
                Email = userEmail,
                DisplayName = userEmail, // Will be updated if seller info is available
                AccessToken = tokenResponse.AccessToken,
                RefreshToken = tokenResponse.RefreshToken,
                TokenExpiry = DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn),
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                LastSyncAt = DateTime.UtcNow,
                SellerInfo = null // Initialize as null, will be set by calling code if needed
            };

            await _dataService.SaveAccountAsync(account);
            return account;
        }

        public async Task<string> GetAccountEmailAsync(string accessToken)
        {
            // ✅ Kiểm tra input
            if (string.IsNullOrEmpty(accessToken))
            {
                throw new ArgumentNullException(nameof(accessToken));
            }

            if (_httpClient == null)
            {
                throw new InvalidOperationException("HttpClient is not initialized");
            }

            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            var response = await _httpClient.GetAsync("https://gmail.googleapis.com/gmail/v1/users/me/profile");
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            using var document = JsonDocument.Parse(json);

            return document.RootElement.GetProperty("emailAddress").GetString() ?? "";
        }

        public async Task<bool> RefreshTokenAsync(string accountId)
        {
            try
            {
                if (string.IsNullOrEmpty(accountId))
                {
                    Console.WriteLine($"❌ AccountId is null or empty");
                    return false;
                }

                var account = await _dataService.GetAccountAsync(accountId);
                if (account == null || string.IsNullOrEmpty(account.RefreshToken))
                {
                    Console.WriteLine($"❌ Account not found or no refresh token");
                    return false;
                }

                var refreshResponse = await RefreshAccessTokenAsync(account.RefreshToken);

                account.AccessToken = refreshResponse.AccessToken;
                account.TokenExpiry = DateTime.UtcNow.AddSeconds(refreshResponse.ExpiresIn);
                account.LastSyncAt = DateTime.UtcNow;

                await _dataService.UpdateAccountAsync(account);
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error refreshing token: {ex.Message}");
                return false;
            }
        }

        public async Task<List<GmailMessage>> GetEmailsAsync(string accountId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                if (string.IsNullOrEmpty(accountId))
                {
                    Console.WriteLine($"❌ AccountId is null or empty");
                    return new List<GmailMessage>();
                }

                var account = await _dataService.GetAccountAsync(accountId);
                if (account == null)
                {
                    Console.WriteLine($"❌ Account {accountId} not found");
                    return new List<GmailMessage>();
                }

                Console.WriteLine($"🔍 Starting email retrieval for account: {account.Email}");

                if (DateTime.UtcNow >= account.TokenExpiry.AddMinutes(-5))
                {
                    Console.WriteLine($"🔄 Token expiring soon, refreshing...");
                    var refreshSuccess = await RefreshTokenAsync(accountId);
                    if (!refreshSuccess)
                    {
                        Console.WriteLine($"❌ Failed to refresh token for {account.Email}");
                        return new List<GmailMessage>();
                    }
                    account = await _dataService.GetAccountAsync(accountId);
                }

                var query = BuildDateQuery(fromDate, toDate);
                Console.WriteLine($"📅 Using query: '{query}' for date range");

                var emails = await GetEmailsForQueryImproved(account.AccessToken, query, maxResults: 5000);

                Console.WriteLine($"✅ Retrieved {emails.Count} emails for account {account.Email}");

                account.LastSyncAt = DateTime.UtcNow;
                await _dataService.UpdateAccountAsync(account);

                return emails;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error in GetEmailsAsync: {ex.Message}");
                Console.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                return new List<GmailMessage>();
            }
        }

        public async Task<List<GmailMessage>> GetEmailsByQueryAsync(string accountId, string query, int maxResults = 1000)
        {
            try
            {
                if (string.IsNullOrEmpty(accountId))
                {
                    Console.WriteLine($"❌ AccountId is null or empty");
                    return new List<GmailMessage>();
                }

                var account = await _dataService.GetAccountAsync(accountId);
                if (account == null)
                {
                    Console.WriteLine($"❌ Account {accountId} not found");
                    return new List<GmailMessage>();
                }

                if (DateTime.UtcNow >= account.TokenExpiry.AddMinutes(-5))
                {
                    var refreshSuccess = await RefreshTokenAsync(accountId);
                    if (!refreshSuccess)
                    {
                        Console.WriteLine($"❌ Failed to refresh token for {account.Email}");
                        return new List<GmailMessage>();
                    }
                    account = await _dataService.GetAccountAsync(accountId);
                }

                Console.WriteLine($"🔍 Getting emails with query: '{query}' for account: {account.Email}");
                var emails = await GetEmailsForQueryImproved(account.AccessToken, query, maxResults);

                Console.WriteLine($"✅ Retrieved {emails.Count} emails for query: {query}");
                return emails;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error in GetEmailsByQueryAsync: {ex.Message}");
                return new List<GmailMessage>();
            }
        }

        public async Task<bool> ValidateTokenAsync(string accountId)
        {
            try
            {
                if (string.IsNullOrEmpty(accountId))
                {
                    return false;
                }

                var account = await _dataService.GetAccountAsync(accountId);
                if (account == null) return false;

                if (DateTime.UtcNow >= account.TokenExpiry)
                {
                    Console.WriteLine($"⚠️ Token expired for account {account.Email}");
                    return false;
                }

                if (_httpClient == null)
                {
                    Console.WriteLine($"❌ HttpClient is null");
                    return false;
                }

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", account.AccessToken);

                var response = await _httpClient.GetAsync("https://gmail.googleapis.com/gmail/v1/users/me/profile");
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error validating token: {ex.Message}");
                return false;
            }
        }

        public async Task<List<GmailLabel>> GetLabelsAsync(string accountId)
        {
            try
            {
                if (string.IsNullOrEmpty(accountId))
                {
                    return new List<GmailLabel>();
                }

                var account = await _dataService.GetAccountAsync(accountId);
                if (account == null) return new List<GmailLabel>();

                if (DateTime.UtcNow >= account.TokenExpiry.AddMinutes(-5))
                {
                    await RefreshTokenAsync(accountId);
                    account = await _dataService.GetAccountAsync(accountId);
                }

                if (_httpClient == null)
                {
                    return new List<GmailLabel>();
                }

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", account.AccessToken);

                var response = await _httpClient.GetAsync("https://gmail.googleapis.com/gmail/v1/users/me/labels");

                if (!response.IsSuccessStatusCode) return new List<GmailLabel>();

                var json = await response.Content.ReadAsStringAsync();
                using var document = JsonDocument.Parse(json);

                if (!document.RootElement.TryGetProperty("labels", out var labelsElement))
                    return new List<GmailLabel>();

                var labels = new List<GmailLabel>();
                foreach (var labelElement in labelsElement.EnumerateArray())
                {
                    labels.Add(new GmailLabel
                    {
                        Id = labelElement.TryGetProperty("id", out var idEl) ? idEl.GetString() ?? "" : "",
                        Name = labelElement.TryGetProperty("name", out var nameEl) ? nameEl.GetString() ?? "" : "",
                        Type = labelElement.TryGetProperty("type", out var typeEl) ? typeEl.GetString() ?? "" : "",
                        MessageListVisibility = labelElement.TryGetProperty("messageListVisibility", out var msgVisEl) ? msgVisEl.GetInt32() : 0,
                        LabelListVisibility = labelElement.TryGetProperty("labelListVisibility", out var lblVisEl) ? lblVisEl.GetInt32() : 0
                    });
                }

                return labels;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error getting labels: {ex.Message}");
                return new List<GmailLabel>();
            }
        }

        public async Task<GmailQuotaInfo> GetAccountQuotaAsync(string accountId)
        {
            try
            {
                if (string.IsNullOrEmpty(accountId))
                {
                    return new GmailQuotaInfo();
                }

                var account = await _dataService.GetAccountAsync(accountId);
                if (account == null) return new GmailQuotaInfo();

                if (DateTime.UtcNow >= account.TokenExpiry.AddMinutes(-5))
                {
                    await RefreshTokenAsync(accountId);
                    account = await _dataService.GetAccountAsync(accountId);
                }

                if (_httpClient == null)
                {
                    return new GmailQuotaInfo();
                }

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", account.AccessToken);

                var response = await _httpClient.GetAsync("https://gmail.googleapis.com/gmail/v1/users/me/profile");

                if (!response.IsSuccessStatusCode) return new GmailQuotaInfo();

                var json = await response.Content.ReadAsStringAsync();
                using var document = JsonDocument.Parse(json);
                var root = document.RootElement;

                return new GmailQuotaInfo
                {
                    EmailCount = root.TryGetProperty("messagesTotal", out var totalEl) ? totalEl.GetInt32() : 0,
                    StorageUsed = root.TryGetProperty("historyId", out var historyEl) ? historyEl.GetInt64() : 0,
                    StorageLimit = 15L * 1024 * 1024 * 1024, // Default Gmail limit 15GB
                    LastUpdated = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error getting account quota: {ex.Message}");
                return new GmailQuotaInfo();
            }
        }

        public async Task<AccountProcessingStats> GetAccountStatsAsync(string accountId)
        {
            try
            {
                if (string.IsNullOrEmpty(accountId))
                {
                    return new AccountProcessingStats
                    {
                        AccountId = accountId,
                        IsHealthy = false,
                        HealthIssues = new List<string> { "AccountId is null or empty" }
                    };
                }

                var account = await _dataService.GetAccountAsync(accountId);
                if (account == null)
                {
                    return new AccountProcessingStats
                    {
                        AccountId = accountId,
                        IsHealthy = false,
                        HealthIssues = new List<string> { "Account not found" }
                    };
                }

                var processedEmails = await _dataService.GetProcessedEmailsByAccountAsync(account.Email);
                var healthIssues = new List<string>();

                if (DateTime.UtcNow >= account.TokenExpiry)
                {
                    healthIssues.Add("Access token expired");
                }

                if (account.LastSyncAt.HasValue && DateTime.UtcNow - account.LastSyncAt.Value > TimeSpan.FromDays(7))
                {
                    healthIssues.Add("No sync in over 7 days");
                }

                return new AccountProcessingStats
                {
                    AccountId = accountId,
                    AccountEmail = account.Email,
                    TotalEmailsProcessed = processedEmails?.Count ?? 0,
                    LastProcessedDate = processedEmails?.Any() == true ? processedEmails.Max(e => e.ProcessedAt) : DateTime.MinValue,
                    ProcessingRuns = 1,
                    IsHealthy = !healthIssues.Any(),
                    HealthIssues = healthIssues
                };
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error getting account stats: {ex.Message}");
                return new AccountProcessingStats
                {
                    AccountId = accountId,
                    IsHealthy = false,
                    HealthIssues = new List<string> { $"Error retrieving stats: {ex.Message}" }
                };
            }
        }

        // PRIVATE HELPER METHODS

        private async Task<List<GmailMessage>> GetEmailsForQueryImproved(string accessToken, string query, int maxResults)
        {
            try
            {
                Console.WriteLine($"🔍 GetEmailsForQueryImproved - Query: '{query}', MaxResults: {maxResults}");

                // ✅ Kiểm tra input parameters
                if (string.IsNullOrEmpty(accessToken))
                {
                    Console.WriteLine($"❌ AccessToken is null or empty");
                    return new List<GmailMessage>();
                }

                var messageIds = await GetMessageIdsWithRetry(accessToken, query, maxResults);
                Console.WriteLine($"📧 Found {messageIds.Count} message IDs for query: '{query}'");

                if (!messageIds.Any())
                {
                    Console.WriteLine($"⚠️ No message IDs found for query: '{query}'");
                    return new List<GmailMessage>();
                }

                var messages = new List<GmailMessage>();
                var batchSize = 10;
                var processedCount = 0;

                for (int i = 0; i < messageIds.Count; i += batchSize)
                {
                    var batch = messageIds.Skip(i).Take(batchSize).ToList();
                    Console.WriteLine($"📬 Processing batch {i / batchSize + 1}/{(messageIds.Count + batchSize - 1) / batchSize}, messages {i + 1}-{Math.Min(i + batchSize, messageIds.Count)}");

                    // ✅ Filter out null or empty messageIds
                    var validMessageIds = batch.Where(id => !string.IsNullOrEmpty(id)).ToList();

                    if (!validMessageIds.Any())
                    {
                        Console.WriteLine($"⚠️ No valid message IDs in this batch");
                        continue;
                    }

                    var batchTasks = validMessageIds.Select(messageId => GetSingleMessageDetailsWithRetry(accessToken, messageId));
                    var batchResults = await Task.WhenAll(batchTasks);

                    foreach (var message in batchResults)
                    {
                        if (message != null)
                        {
                            messages.Add(message);
                            processedCount++;

                            // ✅ Safe null checks for subject extraction
                            var subject = "No Subject";
                            try
                            {
                                if (message.Headers != null && message.Headers.Any())
                                {
                                    var subjectHeader = message.Headers.FirstOrDefault(h => h != null && !string.IsNullOrEmpty(h.Name) && h.Name.Equals("Subject", StringComparison.OrdinalIgnoreCase));
                                    if (subjectHeader != null && !string.IsNullOrEmpty(subjectHeader.Value))
                                    {
                                        subject = subjectHeader.Value;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"⚠️ Error extracting subject for logging: {ex.Message}");
                            }

                            if (processedCount % 50 == 0)
                            {
                                var displaySubject = subject.Length > 50 ? subject.Substring(0, 50) + "..." : subject;
                                Console.WriteLine($"✅ [{processedCount}] Processed: {displaySubject}");
                            }
                        }
                    }

                    if (i + batchSize < messageIds.Count)
                    {
                        await Task.Delay(500);
                        Console.WriteLine($"⏳ Batch delay... ({messages.Count} messages retrieved so far)");
                    }
                }

                Console.WriteLine($"🎯 Successfully retrieved {messages.Count} detailed messages out of {messageIds.Count} IDs");
                return messages;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ GetEmailsForQueryImproved failed: {ex.Message}");
                Console.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                return new List<GmailMessage>();
            }
        }

        private async Task<List<string>> GetMessageIdsWithRetry(string accessToken, string query, int maxResults)
        {
            var messageIds = new List<string>();
            string? nextPageToken = null;
            var remainingResults = maxResults;
            var retryCount = 0;
            const int maxRetries = 3;

            do
            {
                try
                {
                    var batchSize = Math.Min(500, remainingResults);
                    var url = $"https://gmail.googleapis.com/gmail/v1/users/me/messages?maxResults={batchSize}";

                    if (!string.IsNullOrEmpty(query))
                        url += $"&q={Uri.EscapeDataString(query)}";

                    if (!string.IsNullOrEmpty(nextPageToken))
                        url += $"&pageToken={nextPageToken}";

                    Console.WriteLine($"🌐 Fetching message IDs: {url}");

                    _httpClient.DefaultRequestHeaders.Clear();
                    _httpClient.DefaultRequestHeaders.Authorization =
                        new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

                    var response = await _httpClient.GetAsync(url);

                    if (response.StatusCode == HttpStatusCode.TooManyRequests)
                    {
                        Console.WriteLine($"⏳ Rate limited, waiting...");
                        await Task.Delay((int)(Math.Pow(2, retryCount) * 1000));
                        retryCount++;
                        if (retryCount <= maxRetries) continue;
                    }

                    if (!response.IsSuccessStatusCode)
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        Console.WriteLine($"❌ API Error: {response.StatusCode} - {errorContent}");
                        break;
                    }

                    var json = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"📄 Response length: {json.Length} characters");

                    using var document = JsonDocument.Parse(json);
                    var root = document.RootElement;

                    if (root.TryGetProperty("messages", out var messagesElement))
                    {
                        foreach (var messageElement in messagesElement.EnumerateArray())
                        {
                            if (messageElement.TryGetProperty("id", out var idElement))
                            {
                                var messageId = idElement.GetString();
                                if (!string.IsNullOrEmpty(messageId))
                                {
                                    messageIds.Add(messageId);
                                }
                            }
                        }
                    }
                    else
                    {
                        Console.WriteLine($"⚠️ No 'messages' property found in response");
                    }

                    nextPageToken = null;
                    if (root.TryGetProperty("nextPageToken", out var tokenElement))
                    {
                        nextPageToken = tokenElement.GetString();
                    }

                    remainingResults -= batchSize;
                    retryCount = 0;

                    Console.WriteLine($"📊 Batch complete. IDs so far: {messageIds.Count}, Remaining: {remainingResults}");

                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Error fetching message IDs: {ex.Message}");
                    retryCount++;
                    if (retryCount <= maxRetries)
                    {
                        await Task.Delay((int)(Math.Pow(2, retryCount) * 1000));
                        continue;
                    }
                    break;
                }

            } while (!string.IsNullOrEmpty(nextPageToken) && remainingResults > 0 && retryCount <= maxRetries);

            return messageIds;
        }

        private async Task<GmailMessage?> GetSingleMessageDetailsWithRetry(string accessToken, string messageId)
        {
            var retryCount = 0;
            const int maxRetries = 3;

            // ✅ Add null checks and validation
            if (string.IsNullOrEmpty(accessToken))
            {
                Console.WriteLine($"❌ GetSingleMessageDetailsWithRetry: accessToken is null or empty");
                return null;
            }

            if (string.IsNullOrEmpty(messageId))
            {
                Console.WriteLine($"❌ GetSingleMessageDetailsWithRetry: messageId is null or empty");
                return null;
            }

            if (_httpClient == null)
            {
                Console.WriteLine($"❌ GetSingleMessageDetailsWithRetry: _httpClient is null");
                return null;
            }

            if (_disposed)
            {
                Console.WriteLine($"❌ GetSingleMessageDetailsWithRetry: Service has been disposed");
                return null;
            }

            while (retryCount <= maxRetries)
            {
                try
                {
                    // ✅ Additional safety checks before HTTP operations
                    if (_httpClient.DefaultRequestHeaders == null)
                    {
                        Console.WriteLine($"❌ GetSingleMessageDetailsWithRetry: DefaultRequestHeaders is null");
                        return null;
                    }

                    _httpClient.DefaultRequestHeaders.Clear();

                    // ✅ Safe authorization header creation
                    try
                    {
                        _httpClient.DefaultRequestHeaders.Authorization =
                            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                    }
                    catch (Exception authEx)
                    {
                        Console.WriteLine($"❌ Error setting authorization header: {authEx.Message}");
                        return null;
                    }

                    var url = $"https://gmail.googleapis.com/gmail/v1/users/me/messages/{messageId}?format=full";
                    Console.WriteLine($"🔍 Making request to: {url}");

                    var response = await _httpClient.GetAsync(url);

                    // ✅ Check if response is null
                    if (response == null)
                    {
                        Console.WriteLine($"❌ GetSingleMessageDetailsWithRetry: HTTP response is null for message {messageId}");
                        return null;
                    }

                    Console.WriteLine($"📡 Response status: {response.StatusCode} for message {messageId}");

                    if (response.StatusCode == HttpStatusCode.TooManyRequests)
                    {
                        Console.WriteLine($"⏳ Rate limited for message {messageId}, waiting...");
                        await Task.Delay((int)(Math.Pow(2, retryCount) * 1000));
                        retryCount++;
                        continue;
                    }

                    if (!response.IsSuccessStatusCode)
                    {
                        var errorContent = "";
                        try
                        {
                            if (response.Content != null)
                            {
                                errorContent = await response.Content.ReadAsStringAsync();
                            }
                        }
                        catch (Exception contentEx)
                        {
                            Console.WriteLine($"❌ Error reading error content: {contentEx.Message}");
                        }
                        Console.WriteLine($"❌ Message details error: {response.StatusCode} - {errorContent}");
                        return null;
                    }

                    // ✅ Safe content reading
                    string json = "";
                    try
                    {
                        if (response.Content == null)
                        {
                            Console.WriteLine($"❌ GetSingleMessageDetailsWithRetry: Response content is null for message {messageId}");
                            return null;
                        }
                        json = await response.Content.ReadAsStringAsync();
                    }
                    catch (Exception contentEx)
                    {
                        Console.WriteLine($"❌ Error reading response content: {contentEx.Message}");
                        return null;
                    }

                    if (string.IsNullOrEmpty(json))
                    {
                        Console.WriteLine($"❌ GetSingleMessageDetailsWithRetry: Empty JSON response for message {messageId}");
                        return null;
                    }

                    Console.WriteLine($"📄 JSON response length: {json.Length} characters for message {messageId}");

                    // ✅ Safe JSON document parsing
                    JsonDocument document = null;
                    JsonElement root;
                    try
                    {
                        document = JsonDocument.Parse(json);
                        if (document == null)
                        {
                            Console.WriteLine($"❌ GetSingleMessageDetailsWithRetry: JsonDocument.Parse returned null for message {messageId}");
                            return null;
                        }
                        root = document.RootElement;
                    }
                    catch (JsonException jsonEx)
                    {
                        Console.WriteLine($"❌ JSON parsing error for message {messageId}: {jsonEx.Message}");
                        Console.WriteLine($"📄 JSON content preview: {json.Substring(0, Math.Min(500, json.Length))}...");
                        document?.Dispose();
                        return null;
                    }
                    catch (Exception parseEx)
                    {
                        Console.WriteLine($"❌ JSON document parsing error for message {messageId}: {parseEx.Message}");
                        document?.Dispose();
                        return null;
                    }

                    using (document)
                    {
                        // ✅ Safe message object creation
                        GmailMessage message = null;
                        try
                        {
                            message = new GmailMessage
                            {
                                Id = root.TryGetProperty("id", out var idElement) ? idElement.GetString() ?? "" : "",
                                Headers = ExtractHeadersImproved(root),
                                Body = ExtractBodyImproved(root),
                                InternalDate = ExtractInternalDate(root)
                            };
                        }
                        catch (Exception msgEx)
                        {
                            Console.WriteLine($"❌ Error creating GmailMessage object for message {messageId}: {msgEx.Message}");
                            return null;
                        }

                        if (message == null)
                        {
                            Console.WriteLine($"❌ GetSingleMessageDetailsWithRetry: Created message object is null for message {messageId}");
                            return null;
                        }

                        if (message.Headers == null)
                        {
                            Console.WriteLine($"⚠️ GetSingleMessageDetailsWithRetry: Headers is null for message {messageId}");
                            message.Headers = new List<GmailHeader>();
                        }

                        Console.WriteLine($"✅ Successfully created message object for {messageId}");
                        return message;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ GetSingleMessageDetailsWithRetry error (attempt {retryCount + 1}): {ex.Message}");
                    Console.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                    Console.WriteLine($"❌ MessageId: {messageId}");
                    Console.WriteLine($"❌ AccessToken length: {accessToken?.Length ?? 0}");

                    retryCount++;
                    if (retryCount <= maxRetries)
                    {
                        await Task.Delay((int)(Math.Pow(2, retryCount) * 1000));
                    }
                }
            }

            return null;
        }

        private string BuildDateQuery(DateTime? fromDate, DateTime? toDate)
        {
            var queryParts = new List<string>();

            if (fromDate.HasValue)
            {
                var fromDateStr = fromDate.Value.ToString("yyyy/MM/dd");
                queryParts.Add($"after:{fromDateStr}");
            }

            if (toDate.HasValue)
            {
                var toDateStr = toDate.Value.ToString("yyyy/MM/dd");
                queryParts.Add($"before:{toDateStr}");
            }

            return string.Join(" ", queryParts);
        }

        private long ExtractInternalDate(JsonElement root)
        {
            try
            {
                if (root.TryGetProperty("internalDate", out var dateElement))
                {
                    if (dateElement.ValueKind == JsonValueKind.Number)
                    {
                        return dateElement.GetInt64();
                    }
                    else if (dateElement.ValueKind == JsonValueKind.String)
                    {
                        var dateString = dateElement.GetString();
                        if (long.TryParse(dateString, out var dateValue))
                        {
                            return dateValue;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Error extracting internal date: {ex.Message}");
            }

            return DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        }

        private List<GmailHeader> ExtractHeadersImproved(JsonElement messageJson)
        {
            var headers = new List<GmailHeader>();

            try
            {
                if (messageJson.TryGetProperty("payload", out var payload) &&
                    payload.TryGetProperty("headers", out var headersArray) &&
                    headersArray.ValueKind == JsonValueKind.Array)
                {
                    foreach (var header in headersArray.EnumerateArray())
                    {
                        var name = header.TryGetProperty("name", out var nameElement) && nameElement.ValueKind == JsonValueKind.String
                            ? nameElement.GetString() ?? "" : "";
                        var value = header.TryGetProperty("value", out var valueElement) && valueElement.ValueKind == JsonValueKind.String
                            ? valueElement.GetString() ?? "" : "";

                        if (!string.IsNullOrEmpty(name))
                        {
                            headers.Add(new GmailHeader { Name = name, Value = value });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Error extracting headers: {ex.Message}");
            }

            return headers;
        }

        private string ExtractBodyImproved(JsonElement messageJson)
        {
            try
            {
                if (messageJson.TryGetProperty("payload", out var payload))
                {
                    return ExtractBodyRecursiveImproved(payload);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Error extracting body: {ex.Message}");
            }

            return "";
        }

        private string ExtractBodyRecursiveImproved(JsonElement part)
        {
            var body = new StringBuilder();

            try
            {
                if (part.TryGetProperty("body", out var bodyPart) &&
                    bodyPart.TryGetProperty("data", out var data))
                {
                    var encodedData = data.GetString();
                    if (!string.IsNullOrEmpty(encodedData))
                    {
                        var decodedBody = DecodeBase64Url(encodedData);
                        body.Append(decodedBody);
                    }
                }

                if (part.TryGetProperty("parts", out var parts))
                {
                    foreach (var subPart in parts.EnumerateArray())
                    {
                        body.Append(ExtractBodyRecursiveImproved(subPart));
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Error in recursive body extraction: {ex.Message}");
            }

            return body.ToString();
        }

        private string DecodeBase64Url(string base64Url)
        {
            if (string.IsNullOrEmpty(base64Url)) return "";

            var base64 = base64Url.Replace('-', '+').Replace('_', '/');
            switch (base64.Length % 4)
            {
                case 2: base64 += "=="; break;
                case 3: base64 += "="; break;
            }

            try
            {
                var bytes = Convert.FromBase64String(base64);
                return Encoding.UTF8.GetString(bytes);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ Base64 decode error: {ex.Message}");
                return "";
            }
        }

        private async Task<TokenResponse> ExchangeCodeForTokensAsync(string authorizationCode)
        {
            var requestData = new Dictionary<string, string>
            {
                {"code", authorizationCode},
                {"client_id", _clientId},
                {"client_secret", _clientSecret},
                {"redirect_uri", _redirectUri},
                {"grant_type", "authorization_code"}
            };

            var content = new FormUrlEncodedContent(requestData);
            var response = await _httpClient.PostAsync("https://oauth2.googleapis.com/token", content);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            using var document = JsonDocument.Parse(json);
            var root = document.RootElement;

            return new TokenResponse
            {
                AccessToken = root.GetProperty("access_token").GetString() ?? "",
                RefreshToken = root.TryGetProperty("refresh_token", out var refreshElement)
                    ? refreshElement.GetString() ?? ""
                    : "",
                ExpiresIn = root.GetProperty("expires_in").GetInt32()
            };
        }

        private async Task<TokenResponse> RefreshAccessTokenAsync(string refreshToken)
        {
            var requestData = new Dictionary<string, string>
            {
                {"refresh_token", refreshToken},
                {"client_id", _clientId},
                {"client_secret", _clientSecret},
                {"grant_type", "refresh_token"}
            };

            var content = new FormUrlEncodedContent(requestData);
            var response = await _httpClient.PostAsync("https://oauth2.googleapis.com/token", content);
            response.EnsureSuccessStatusCode();

            var json = await response.Content.ReadAsStringAsync();
            using var document = JsonDocument.Parse(json);
            var root = document.RootElement;

            return new TokenResponse
            {
                AccessToken = root.GetProperty("access_token").GetString() ?? "",
                RefreshToken = refreshToken,
                ExpiresIn = root.GetProperty("expires_in").GetInt32()
            };
        }

        // ✅ PROPER IDISPOSABLE IMPLEMENTATION
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    _httpClient?.Dispose();
                }
                // Dispose unmanaged resources if any
                _disposed = true;
            }
        }

        // Finalizer (destructor)
        ~GmailService()
        {
            Dispose(false);
        }
    }

    public class TokenResponse
    {
        public string AccessToken { get; set; } = "";
        public string RefreshToken { get; set; } = "";
        public int ExpiresIn { get; set; }
    }
}