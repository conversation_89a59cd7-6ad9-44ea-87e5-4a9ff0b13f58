﻿using System.Text;
using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services;
using LSB.SellerMailTracker.API.Services.Imple;
using LSB.SellerMailTracker.API.Services.Interfaces;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers();
builder.Services.AddOpenApi();
builder.Services.AddSwaggerGen();

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp",
        policy =>
        {
            policy.WithOrigins("http://localhost:5173", "https://localhost:5173")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
        });
});

builder.Services.AddHttpClient();

builder.Services.AddSingleton<ITemplateService, TemplateService>();
builder.Services.AddSingleton<IAccountService, AccountService>();
builder.Services.AddSingleton<IFilterService, FilterService>();
builder.Services.AddSingleton<IEmailService, EmailService>();
builder.Services.AddSingleton<IStatsService, StatsService>();
builder.Services.AddSingleton<IAuthService, AuthService>();

builder.Services.AddSingleton<IDataService, DataService>();

builder.Services.AddSingleton<IGmailService, GmailService>();
builder.Services.AddSingleton<IEmailFilterService, EmailFilterService>();

builder.Services.AddSingleton<IAutoScanService, AutoScanService>();

builder.Services.AddHostedService<AutoScanBackgroundService>();

builder.Services.AddScoped<IHtmlGeneratorService, HtmlGeneratorService>();

builder.Services.Configure<GoogleOAuthSettings>(
    builder.Configuration.GetSection("GoogleOAuth"));

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"],
            ValidAudience = builder.Configuration["Jwt:Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ?? ""))
        };
    });

builder.Services.AddLogging(logging =>
{
    logging.AddConsole();
    logging.AddDebug();
    if (builder.Environment.IsDevelopment())
    {
        logging.SetMinimumLevel(LogLevel.Debug);
    }
    else
    {
        logging.SetMinimumLevel(LogLevel.Information);
    }
});

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Gmail Processor API v1");
        c.RoutePrefix = "swagger";
    });
}

app.UseHttpsRedirection();
app.UseCors("AllowReactApp");
app.UseAuthentication();
app.UseAuthorization();
app.UseDefaultFiles();      
app.UseStaticFiles();

app.MapFallbackToFile("index.html");

app.MapControllers();

app.Run();